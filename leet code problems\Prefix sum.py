# Range sum query - immutable
class NumArray(object):

    def __init__(self, nums):
        """
        :type nums: List[int]
        """
        n=len(nums)
        p=[0]*(n+1)
        for i in range(n):
            p[i+1]=p[i]+nums[i]
        self.p=p
        self.n=n
        self.nums=nums
        print(self.p)

    def sumRange(self, left, right):
        """
        :type left: int
        :type right: int
        :rtype: int
        """
        if left < 0 or right > len(self.p) - 1 or left > right:
            return 0
        return self.p[right+1]-self.p[left]
        
numArray=NumArray([-2, 0, 3, -5, 2, -1])
print(numArray.sumRange(0,2))
print(numArray.sumRange(2, 5))
print(numArray.sumRange(0, 5))

# Find Pivot index
class Solution(object):
    def pivotIndex(self, nums):
        """
        :type nums: List[int]
        :rtype: int
        """
        total_sum=sum(nums)
        left_sum=0
        for i in range(len(nums)):
            right_sum = total_sum - left_sum - nums[i]
            if left_sum == right_sum:
                return i
            left_sum += nums[i]
        return -1

s=Solution()
print(s.pivotIndex([1,7,3,6,5,6]))
            
# Minimum value to get positive
class Solution(object):
    def minStartValue(self, nums):
        """
        :type nums: List[int]
        :rtype: int
        """
        min_val=0
        sum_val=0
        for i in range(len(nums)):
            sum_val += nums[i]
            min_val = min(min_val, sum_val)
        return 1 - min_val

s=Solution()
print(s.minStartValue([-3,2,-3,4,2]))

# Maximum score after spliting a string
class Solution(object):
    def maxScore(self, s):
        """
        :type s: str
        :rtype: int
        """
        ones_in_the_right = 0
        for char in s:
            if char == '1':
                ones_in_the_right += 1
        zeros_in_the_left=0
        max_score=0
        for i in range(len(s)-1):
            if s[i] == '0':
                zeros_in_the_left += 1
            else:
                ones_in_the_right -= 1
            current_score = zeros_in_the_left + ones_in_the_right
            max_score=max(max_score,current_score)
        return max_score

s=Solution()
print(s.maxScore("011101"))
  
# Product of array except itself
class Solution(object):
    def productExceptSelf(self, nums):
        """
        :type nums: List[int]
        :rtype: List[int]
        """
        n=len(nums)
        left_product=[1]*n
        right_product=[1]*n
        for i in range(1,n):
            left_product[i]=left_product[i-1]*nums[i-1]
        for i in range(n-2,-1,-1):
            right_product[i]=right_product[i+1]*nums[i+1]
        return [left_product[i]*right_product[i] for i in range(n)]

s=Solution()
print(s.productExceptSelf([1,2,3,4]))

# Random point in Non-overlapping Rectangles
import random
class Solution(object):

    def __init__(self, rects):
        """
        :type rects: List[List[int]]
        """
        self.rects = rects
        self.areas = []
        self.total_area = 0

        # Calculate area for each rectangle and cumulative areas
        for rect in rects:
            x1, y1, x2, y2 = rect
            area = (x2 - x1 + 1) * (y2 - y1 + 1)
            self.total_area += area
            self.areas.append(self.total_area)

    def pick(self):
        """
        :rtype: List[int]
        """
        # Pick a random area point
        target = random.randint(1, self.total_area)

        # Find which rectangle this point belongs to
        rect_idx = 0
        for i, area in enumerate(self.areas):
            if target <= area:
                rect_idx = i
                break

        # Pick random point within the selected rectangle
        x1, y1, x2, y2 = self.rects[rect_idx]
        x = random.randint(x1, x2)
        y = random.randint(y1, y2)
        return [x, y]

# Fixed input format - should be list of rectangles, each rectangle is [x1, y1, x2, y2]
solution = Solution([[-2, -2, 1, 1], [2, 2, 4, 6]])
print(solution.pick())

# Sub array sum equals k
class Solution(object):
    def subarraySum(self, nums, k):
        """
        :type nums: List[int]
        :type k: int
        :rtype: int
        """
        count=0
        p_sum=0
        # Create a dictionary
        sub_dict={}
        sub_dict[0]=1
        for num in nums:
            p_sum += num
            temp=p_sum-k
            if temp in sub_dict:
                count += sub_dict.get(temp)
        
            sub_dict[p_sum] = sub_dict.get(p_sum,0) + 1
        return count

s=Solution()
print(s.subarraySum([1,2,3],3))
print(s.subarraySum([1,1,1],2))

# Continous subarray sum
class Solution(object):
    def checkSubarraySum(self, nums, k):
        """
        :type nums: List[int]
        :type k: int
        :rtype: bool
        """
        r={0:-1}
        sub_total=0

        for i,num in enumerate(nums):
            sub_total += num
            rem=sub_total%k
            if rem not in r:
                r[rem]=i
            elif i - r[rem] >= 2:
                return True
        return False
    
s=Solution()
print(s.checkSubarraySum([23,2,4,6,7],6))
print(s.checkSubarraySum([23,2,6,4,7],13)) 
print(s.checkSubarraySum([2,4,3],6))    

# Sub array sums divisible by k
class Solution(object):
    def subarraysDivByK(self, nums, k):
        """
        :type nums: List[int]
        :type k: int
        :rtype: int
        """
        count=0
        sub_total=0
        for i in range(len(nums)):
            for j in range(len(nums)):
                if sub_total == k:
                    count += 1
            sub_total += nums[i][j]
        return count

s=Solution()
print(s.subarraysDivByK([4,5,0,-2,-3,1],5))
        